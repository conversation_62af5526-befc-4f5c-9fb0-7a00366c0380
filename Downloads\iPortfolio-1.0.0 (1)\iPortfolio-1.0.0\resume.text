%------------------------
% Resume Template
% Author : <PERSON><PERSON><PERSON><PERSON> Singh
% Github : https://github.com/xprilion
% License : MIT
%------------------------

\documentclass[a4paper,20pt]{article}
\usepackage{fontawesome5}
\usepackage{latexsym}
\usepackage[empty]{fullpage}
\usepackage{titlesec}
\usepackage{marvosym}
\usepackage[usenames,dvipsnames]{color}
\usepackage{verbatim}
\usepackage{enumitem}
\usepackage[pdftex]{hyperref}
\usepackage{fancyhdr}
\usepackage{xcolor}
\usepackage{hyperref}

\hypersetup{
    colorlinks=true,
    linkcolor=blue,
    urlcolor=blue,
    citecolor=blue
}

\usepackage{emoji}

\pagestyle{fancy}
\fancyhf{} % clear all header and footer fields
\fancyfoot{}
\renewcommand{\headrulewidth}{0pt}
\renewcommand{\footrulewidth}{0pt}

% Adjust margins
\addtolength{\oddsidemargin}{-0.530in}
\addtolength{\evensidemargin}{-0.375in}
\addtolength{\textwidth}{1in}
\addtolength{\topmargin}{-.45in}
\addtolength{\textheight}{1in}

\urlstyle{rm}

\raggedbottom
\raggedright
\setlength{\tabcolsep}{0in}

% Sections formatting
\titleformat{\section}{
  \vspace{-10pt}\scshape\raggedright\large
}{}{0em}{}[\color{black}\titlerule \vspace{-6pt}]

%-------------------------
% Custom commands
\newcommand{\resumeItem}[2]{
  \item\small{
    \textbf{#1}{: #2 \vspace{-2pt}}
  }
}

\newcommand{\resumeItemWithoutTitle}[1]{
  \item\small{
    {\vspace{-2pt}}
  }
}

\newcommand{\resumeSubheading}[4]{
  \vspace{-1pt}\item
    \begin{tabular*}{0.97\textwidth}{l@{\extracolsep{\fill}}r}
      \textbf{#1} & #2 \\
      \textit{#3} & \textit{#4} \\
    \end{tabular*}\vspace{-5pt}
}


\newcommand{\resumeSubItem}[2]{\resumeItem{#1}{#2}\vspace{-3pt}}

\renewcommand{\labelitemii}{$\circ$}

\newcommand{\resumeSubHeadingListStart}{\begin{itemize}[leftmargin=*]}
\newcommand{\resumeSubHeadingListEnd}{\end{itemize}}
\newcommand{\resumeItemListStart}{\begin{itemize}}
\newcommand{\resumeItemListEnd}{\end{itemize}\vspace{-5pt}}

%-----------------------------
%%%%%%  CV STARTS HERE  %%%%%%

\begin{document}

%----------HEADING-----------------
\begin{tabular*}{\textwidth}{l@{\extracolsep{\fill}}r}
  \textbf{{\LARGE Surendar Kumar H P}} & Email: \href{mailto:<EMAIL>}{<EMAIL>}\\
  \href{https://linkedin.com/in/surendar-kumar03/}{Linkedin: linkedin.com/in/surendar-kumar03/} & Mobile:~~~+91-8971291369 \\
  \href{https://github.com/SurendarKumar3}{Github: ~~github.com/SurendarKumar3} \\
\end{tabular*}


%-----------EDUCATION-----------------
\section{~~Education}
\resumeSubHeadingListStart

  \resumeSubheading
    {Adichunchanagiri Institute Of Technology}{Chikmagalur, India}
    {Bachelor of Engineering - Computer Science and Engineering \textbar{} GPA: 9.1}{December 2022 - April 2026}
    
  \resumeSubheading
    {St. Mary's PU College}{Chikmagalur, Karnataka }
    {Pre-University Course (PCMC) \textbar{} Percentage: 96.8\%}{June 2020 - April 2022}

  \resumeSubheading
    {St. Mary's International School}{ Chikmagalur, Karnataka}
    {Secondary School Certificate (10th Grade) \textbar{} Percentage: 92.6\%}{June 2019 - April 2020}

\resumeSubHeadingListEnd

    
\vspace{-5pt}
\section{Skills Summary}
    \resumeSubHeadingListStart
    \resumeSubItem{Languages}{~~~~~~C++, C, Python, Java}
    %\resumeSubItem{Frameworks}{~~~~React, Flask, Django, Flutter, Sklearn}
    \resumeSubItem{Tools}{~~~~~~~~~~~~~~GIT, MySQL}
    \resumeSubItem{Platforms}{~~~~~~~Web, Windows}
    \resumeSubItem{Certifications}{~~~~~~NPTEL Programming in Java (Jan--Apr 2025), NPTEL Machine Learning (Jan--Mar 2025), \href{https://www.credly.com/badges/167b9c8f-7334-47a1-81c4-d828e90d215c}{Academic Process Mining Fundamentals (Celonis, Apr 2025)}, \href{https://credly.com/go/7oecHgny}{AWS Academy Graduate - Cloud Architecting (Jul 2025)}}
    \resumeSubHeadingListEnd

%-----------PROJECTS-----------------
\vspace{-5pt}
\section{Projects}
\resumeSubHeadingListStart

\resumeSubItem{Binary Bot -- Binary Trading Signal Generator (Real-Time Market Analysis, Trading Algorithms, Notification System)}{Real-time binary trading signal generator supporting 10+ currency pairs, using Smart Money Concepts (SMC), RSI, and volume-based strategies on 5-minute candles. Generates actionable signals with pop-up notifications and allows external strategy integration. Includes a 6-month backtesting engine. Tech: Python, Flask, JavaScript, TradingView API, TailwindCSS (May '25)}
\vspace{2pt}

\resumeSubItem{E-Shiksha -- Competitive Training Web App (E-Learning, Gamification, Performance Tracking)}{Interactive platform offering quizzes, mock tests, progress tracking, and study material access tailored for competitive exams. Features user authentication, leaderboard rankings, and AI-based question suggestions for personalized learning paths. Tech: ReactJS, Node.js, MongoDB, TailwindCSS (Mar'25)}
\vspace{2pt}

\resumeSubItem{InstaScan -- Food Content Recognition \& Calorie Calculator (Image Processing, Nutrition AI, Chatbot Integration)}{Mobile/web app that scans food product labels via images to extract ingredients, display nutritional information, and estimate calorie content. Includes a conversational chatbot for health tips, meal tracking, and diet recommendations. Tech: Python, OpenCV, Flask, ReactJS, Dialogflow (Feb'25)}
\vspace{2pt}

\resumeSubItem{PhishShield -- Phishing Website Detection Tool (Cybersecurity, Web Scraping, Permutation Analysis)}{Detects phishing domains using DNS permutation-based analysis, WHOIS checks, and SSL certificate validation. Built-in URL similarity scoring helps identify impersonation. Web interface supports live URL checks and blacklist integration. Tech: Python, Flask, DnsTwist, OpenSSL, ReactJS (Nov '24)}

\resumeSubHeadingListEnd

%-----------HACKATHONS-----------------
\vspace{-5pt}
\section{Hackathons}
\resumeSubHeadingListStart

\resumeSubItem{hackCSE|erate -- Adichunchanagiri Institute of Technology}{Participated in 24-hour national-level hackathon organized by Dept. of CSE in association with Computer Society of India (CSI), focusing on innovative software solutions (November 14-15, 2024).}

\vspace{2pt}
\resumeSubItem{State-Level Hackathon -- Malnad College of Engineering}{Demonstrated exceptional technical skills and teamwork in 6-hour state-level event organized by Team DevOps, recognized for innovative contributions (November 22, 2024).}

\vspace{2pt}
\resumeSubItem{Tech Tank 2025 -- RV College of Engineering, Bengaluru}{Selected for Round 2 of Tech Tank 2025, a Shark Tank--inspired 12-hour hackathon organized by RVCE ACM Student Chapter and Google Developer Groups (GDG) RVCE on April 11, 2025.}

\vspace{2pt}
\resumeSubItem{Sentinel Hack 5.0 -- K.S. Institute of Technology, Bengaluru}{Participated in a 24-hour state-level hackathon organized by the Department of Computer Science \& Engineering on April 28--29, 2025. Focused on AI and cybersecurity innovations.}

\vspace{2pt}
\resumeSubItem{IGNITEX 2025 -- Adichunchanagiri University, BGSIT, Karnataka}{Participated in a 24-hour national-level software and hardware hackathon organized by the Department of Computer Science \& Engineering in collaboration with NAIN and IEEE on April 29--30, 2025.}

\resumeSubHeadingListEnd
\end{document}
