<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta content="width=device-width, initial-scale=1.0" name="viewport">
  <title><PERSON><PERSON><PERSON> - Portfolio</title>
  <meta content="Portfolio of Surendar Kumar - Computer Science Engineering Student specializing in Software Development, Machine Learning, and Cybersecurity" name="description">
  <meta content="<PERSON><PERSON><PERSON>, Computer Science, Software Developer, Python, Java, C++, Machine Learning, Cybersecurity, Portfolio" name="keywords">

  <!-- Favicons -->
  <link href="assets/img/placeholder-favicon.png" rel="icon">
  <link href="assets/img/placeholder-apple-touch-icon.png" rel="apple-touch-icon">

  <!-- Fonts -->
  <link href="https://fonts.googleapis.com" rel="preconnect">
  <link href="https://fonts.gstatic.com" rel="preconnect" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Raleway:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap" rel="stylesheet">

  <!-- Vendor CSS Files -->
  <link href="assets/vendor/bootstrap/css/bootstrap.min.css" rel="stylesheet">
  <link href="assets/vendor/bootstrap-icons/bootstrap-icons.css" rel="stylesheet">
  <link href="assets/vendor/aos/aos.css" rel="stylesheet">
  <link href="assets/vendor/glightbox/css/glightbox.min.css" rel="stylesheet">
  <link href="assets/vendor/swiper/swiper-bundle.min.css" rel="stylesheet">

  <!-- Main CSS File -->
  <link href="assets/css/main.css" rel="stylesheet">

  <!-- Custom CSS for Enhanced Design -->
  <style>
    /* Header Styles */
    .shreyanka-header {
      background: rgba(0, 0, 0, 0.95);
      backdrop-filter: blur(10px);
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      padding: 0.5rem 0;
    }

    .brand-name {
      font-size: 1.5rem;
      font-weight: 700;
      color: #ffffff;
      font-style: italic;
      text-decoration: underline;
    }

    .navbar-nav .nav-link {
      color: #cccccc;
      font-weight: 500;
      margin: 0 0.5rem;
      transition: color 0.3s ease;
    }

    .navbar-nav .nav-link:hover {
      color: #ffffff;
    }

    .theme-toggle-btn {
      background: none;
      border: 1px solid rgba(255, 255, 255, 0.2);
      color: #ffffff;
      padding: 0.5rem;
      border-radius: 50%;
      transition: all 0.3s ease;
    }

    .theme-toggle-btn:hover {
      background: rgba(255, 255, 255, 0.1);
      border-color: rgba(255, 255, 255, 0.3);
    }

    /* Hero Section Styles */
    .hero-content {
      padding-left: 2rem;
    }

    .hero-name {
      font-size: 2.5rem;
      font-weight: 700;
      color: #ffffff;
      margin-bottom: 0.5rem;
    }

    .hero-title {
      font-size: 1.2rem;
      color: #cccccc;
      margin-bottom: 2rem;
    }

    .hero-details {
      margin-bottom: 2rem;
    }

    .detail-item {
      margin-bottom: 1.5rem;
    }

    .detail-item strong {
      display: block;
      color: #ffffff;
      font-weight: 600;
      margin-bottom: 0.3rem;
    }

    .detail-item p {
      color: #cccccc;
      margin: 0;
      line-height: 1.5;
    }

    .hero-actions {
      display: flex;
      flex-direction: column;
      gap: 1rem;
    }

    .social-links, .action-buttons {
      display: flex;
      gap: 1rem;
      flex-wrap: wrap;
    }

    .social-btn, .action-btn {
      display: inline-flex;
      align-items: center;
      gap: 0.5rem;
      padding: 0.75rem 1.5rem;
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);
      border-radius: 8px;
      color: #ffffff;
      text-decoration: none;
      font-weight: 500;
      transition: all 0.3s ease;
    }

    .social-btn:hover, .action-btn:hover {
      background: rgba(255, 255, 255, 0.2);
      border-color: rgba(255, 255, 255, 0.3);
      color: #ffffff;
      transform: translateY(-2px);
    }

    .hero-image img {
      max-width: 100%;
      height: auto;
      border-radius: 1rem;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    }

    /* Responsive Design */
    @media (max-width: 991px) {
      .hero-content {
        padding-left: 0;
        margin-top: 2rem;
      }

      .hero-name {
        font-size: 2rem;
      }

      .social-links, .action-buttons {
        justify-content: center;
      }
    }

    /* Enhanced Typography */
    .hero h2 {
      font-weight: 700;
      color: #149ddd;
      text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
    }

    /* Improved Skills Section */
    .skills .progress {
      margin-bottom: 20px;
      border-radius: 10px;
      overflow: hidden;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .skills .progress-bar {
      background: linear-gradient(45deg, #149ddd, #1e7e9a);
      border-radius: 10px;
    }

    /* Enhanced Portfolio Cards */
    .portfolio-item .portfolio-content {
      border-radius: 15px;
      overflow: hidden;
      box-shadow: 0 5px 15px rgba(0,0,0,0.1);
      transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .portfolio-item .portfolio-content:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 25px rgba(0,0,0,0.15);
    }

    /* Improved Service Cards */
    .service-item {
      background: #f8f9fa;
      padding: 30px;
      border-radius: 15px;
      margin-bottom: 30px;
      box-shadow: 0 3px 10px rgba(0,0,0,0.1);
      transition: transform 0.3s ease;
    }

    .service-item:hover {
      transform: translateY(-3px);
      box-shadow: 0 5px 20px rgba(0,0,0,0.15);
    }

    /* Enhanced Contact Section */
    .contact .info-item {
      background: #f8f9fa;
      padding: 25px;
      border-radius: 10px;
      margin-bottom: 20px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    /* Resume Section Enhancements */
    .resume-item {
      background: #f8f9fa;
      padding: 25px;
      border-radius: 10px;
      margin-bottom: 25px;
      border-left: 4px solid #149ddd;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .resume-item h4 {
      color: #149ddd;
      font-weight: 600;
    }

    /* Social Links Enhancement */
    .social-links a {
      background: #149ddd;
      color: white;
      width: 40px;
      height: 40px;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      margin: 0 5px;
      transition: all 0.3s ease;
    }

    .social-links a:hover {
      background: #1e7e9a;
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    }

    /* New Skills Section Styling */
    .skill-item {
      background: #fff;
      padding: 30px 20px;
      border-radius: 15px;
      box-shadow: 0 5px 15px rgba(0,0,0,0.1);
      transition: all 0.3s ease;
      margin-bottom: 30px;
      height: 150px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
    }

    .skill-item:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 25px rgba(0,0,0,0.15);
    }

    .skill-icon {
      font-size: 3rem;
      color: #149ddd;
      margin-bottom: 15px;
    }

    .skill-item h5 {
      color: #333;
      font-weight: 600;
      margin: 0;
      font-size: 1rem;
    }

    /* Dark background for skills section */
    .skills.section.light-background {
      background: #f8f9fa;
    }
  </style>

  <!-- =======================================================
  * Template Name: iPortfolio
  * Template URL: https://bootstrapmade.com/iportfolio-bootstrap-portfolio-websites-template/
  * Updated: Jun 29 2024 with Bootstrap v5.3.3
  * Author: BootstrapMade.com
  * License: https://bootstrapmade.com/license/
  ======================================================== -->
</head>

<body class="index-page">

  <!-- Header -->
  <header class="shreyanka-header fixed-top">
    <nav class="navbar navbar-expand-lg">
      <div class="container-fluid px-4">
        <!-- Brand -->
        <a class="navbar-brand" href="#home">
          <span class="brand-name">Shreyanka</span>
        </a>

        <!-- Mobile Toggle -->
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
          <span class="navbar-toggler-icon"></span>
        </button>

        <!-- Navigation Menu -->
        <div class="collapse navbar-collapse" id="navbarNav">
          <ul class="navbar-nav ms-auto me-3">
            <li class="nav-item">
              <a class="nav-link" href="#about">About</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="#skills">Skills</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="#certificates">Certificates</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="#badges">Badges</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="#achievements">Achievements</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="#projects">Projects</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="#contact">Contact</a>
            </li>
          </ul>

          <!-- Theme Toggle -->
          <button id="theme-toggle" class="theme-toggle-btn" title="Toggle Theme">
            <i class="bi bi-sun-fill" id="theme-icon"></i>
          </button>
        </div>
      </div>
    </nav>
  </header>

  <main class="main" style="margin-left: 0; padding-top: 80px;">

    <!-- Hero Section -->
    <section id="hero" class="hero section dark-background">

      <div class="container">
        <div class="row align-items-center min-vh-100">
          <div class="col-lg-6 col-md-6" data-aos="fade-up" data-aos-delay="100">
            <div class="hero-image">
              <img src="assets/img/home.jpg" alt="Shreyanka Profile Image" class="img-fluid rounded-4">
            </div>
          </div>
          <div class="col-lg-6 col-md-6" data-aos="fade-in" data-aos-delay="200">
            <div class="hero-content">
              <h1 class="hero-name">Shreyanka A Y</h1>
              <p class="hero-title">Final Year Student</p>

              <div class="hero-details">
                <div class="detail-item">
                  <strong>Degree</strong>
                  <p>Bachelor of Engineering in Computer Science</p>
                </div>

                <div class="detail-item">
                  <strong>College</strong>
                  <p>Adichunchanagiri Institute of Technology, Chikkamagaluru-577102</p>
                </div>

                <div class="detail-item">
                  <strong>Expected Graduation</strong>
                  <p>June 2026</p>
                </div>

                <div class="detail-item">
                  <strong>About Me</strong>
                  <p>I am a passionate final year Computer Science student with a strong foundation in programming and software development. I'm interested in machine learning, software development, and web development, and I'm seeking opportunities to apply my skills in a professional environment.</p>
                </div>
              </div>

              <div class="hero-actions">
                <div class="social-links">
                  <a href="https://linkedin.com/in/surendar-kumar03/" target="_blank" class="social-btn">
                    <i class="bi bi-linkedin"></i>
                    <span>LinkedIn Profile</span>
                  </a>
                  <a href="https://github.com/SurendarKumar3" target="_blank" class="social-btn">
                    <i class="bi bi-github"></i>
                    <span>GitHub Profile</span>
                  </a>
                </div>

                <div class="action-buttons">
                  <a href="https://www.salesforce.com/trailblazer/profile" target="_blank" class="action-btn">
                    <i class="bi bi-cloud"></i>
                    <span>Salesforce Trailblazer</span>
                  </a>
                  <a href="Resume (4).pdf" download="Shreyanka_Resume.pdf" class="action-btn">
                    <i class="bi bi-download"></i>
                    <span>Download Resume</span>
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

    </section><!-- /Hero Section -->



    <!-- About Section -->
    <section id="about" class="about section">

      <!-- Section Title -->
      <div class="container section-title" data-aos="fade-up">
        <h2>About</h2>
        <p>I'm a passionate Computer Science Engineering student at Adichunchanagiri Institute Of Technology with a strong GPA of 9.1. I specialize in software development, machine learning, and cybersecurity, with hands-on experience in building innovative solutions through various projects and hackathons.</p>
      </div><!-- End Section Title -->

      <div class="container" data-aos="fade-up" data-aos-delay="100">

        <div class="row gy-4 justify-content-center">
          <div class="col-lg-4">
            <img src="assets/img/placeholder-about-img.jpg" class="img-fluid" alt="">
          </div>
          <div class="col-lg-8 content">
            <h2>Computer Science Engineering Student &amp; Software Developer.</h2>
            <p class="fst-italic py-3">
              Dedicated to creating innovative software solutions with expertise in multiple programming languages and emerging technologies. Currently pursuing B.E. in Computer Science with a focus on practical application through projects and hackathons.
            </p>
            <div class="row">
              <div class="col-lg-6">
                <ul>
                  <li><i class="bi bi-chevron-right"></i> <strong>Degree:</strong> <span>B.E. Computer Science</span></li>
                  <li><i class="bi bi-chevron-right"></i> <strong>GitHub:</strong> <span><a href="https://github.com/SurendarKumar3" target="_blank">SurendarKumar3</a></span></li>
                  <li><i class="bi bi-chevron-right"></i> <strong>Phone:</strong> <span>+91-8971291369</span></li>
                  <li><i class="bi bi-chevron-right"></i> <strong>City:</strong> <span>Chikmagalur, Karnataka</span></li>
                </ul>
              </div>
              <div class="col-lg-6">
                <ul>
                  <li><i class="bi bi-chevron-right"></i> <strong>GPA:</strong> <span>9.1/10</span></li>
                  <li><i class="bi bi-chevron-right"></i> <strong>University:</strong> <span>Adichunchanagiri Institute</span></li>
                  <li><i class="bi bi-chevron-right"></i> <strong>Email:</strong> <span><a href="mailto:<EMAIL>"><EMAIL></a></span></li>
                  <li><i class="bi bi-chevron-right"></i> <strong>Projects:</strong> <span>Available</span></li>
                </ul>
              </div>
            </div>
            <p class="py-3">
              I have developed multiple innovative projects including a real-time binary trading signal generator, an e-learning platform, and cybersecurity tools. My experience spans across web development, machine learning, and competitive programming. I actively participate in hackathons and have been recognized at state and national levels for my technical contributions.
            </p>
          </div>
        </div>

      </div>

    </section><!-- /About Section -->



    <!-- Skills Section -->
    <section id="skills" class="skills section dark-background">

      <!-- Section Title -->
      <div class="container section-title" data-aos="fade-up">
        <h2>My Skills</h2>
        <p>Technical expertise across multiple programming languages, development tools, and emerging technologies</p>
      </div><!-- End Section Title -->

      <div class="container" data-aos="fade-up" data-aos-delay="100">

        <div class="row gy-4 justify-content-center">

          <div class="col-lg-2 col-md-3 col-sm-4 col-6 text-center">
            <div class="skill-item">
              <div class="skill-icon">
                <i class="bi bi-code-slash"></i>
              </div>
              <h5>Python</h5>
            </div>
          </div><!-- End Skills Item -->

          <div class="col-lg-2 col-md-3 col-sm-4 col-6 text-center">
            <div class="skill-item">
              <div class="skill-icon">
                <i class="bi bi-braces"></i>
              </div>
              <h5>Java</h5>
            </div>
          </div><!-- End Skills Item -->

          <div class="col-lg-2 col-md-3 col-sm-4 col-6 text-center">
            <div class="skill-item">
              <div class="skill-icon">
                <i class="bi bi-filetype-html"></i>
              </div>
              <h5>HTML/CSS</h5>
            </div>
          </div><!-- End Skills Item -->

          <div class="col-lg-2 col-md-3 col-sm-4 col-6 text-center">
            <div class="skill-item">
              <div class="skill-icon">
                <i class="bi bi-server"></i>
              </div>
              <h5>Flask</h5>
            </div>
          </div><!-- End Skills Item -->

          <div class="col-lg-2 col-md-3 col-sm-4 col-6 text-center">
            <div class="skill-item">
              <div class="skill-icon">
                <i class="bi bi-filetype-js"></i>
              </div>
              <h5>JavaScript</h5>
            </div>
          </div><!-- End Skills Item -->

          <div class="col-lg-2 col-md-3 col-sm-4 col-6 text-center">
            <div class="skill-item">
              <div class="skill-icon">
                <i class="bi bi-robot"></i>
              </div>
              <h5>Machine Learning</h5>
            </div>
          </div><!-- End Skills Item -->

          <div class="col-lg-2 col-md-3 col-sm-4 col-6 text-center">
            <div class="skill-item">
              <div class="skill-icon">
                <i class="bi bi-bar-chart"></i>
              </div>
              <h5>Power BI</h5>
            </div>
          </div><!-- End Skills Item -->

          <div class="col-lg-2 col-md-3 col-sm-4 col-6 text-center">
            <div class="skill-item">
              <div class="skill-icon">
                <i class="bi bi-database"></i>
              </div>
              <h5>MySQL</h5>
            </div>
          </div><!-- End Skills Item -->

          <div class="col-lg-2 col-md-3 col-sm-4 col-6 text-center">
            <div class="skill-item">
              <div class="skill-icon">
                <i class="bi bi-cloud"></i>
              </div>
              <h5>AWS</h5>
            </div>
          </div><!-- End Skills Item -->

          <div class="col-lg-2 col-md-3 col-sm-4 col-6 text-center">
            <div class="skill-item">
              <div class="skill-icon">
                <i class="bi bi-git"></i>
              </div>
              <h5>Git</h5>
            </div>
          </div><!-- End Skills Item -->

        </div>

      </div>

    </section><!-- /Skills Section -->

    <!-- Resume Section -->
    <section id="resume" class="resume section">

      <!-- Section Title -->
      <div class="container section-title" data-aos="fade-up">
        <h2>Resume</h2>
        <p>Academic excellence combined with practical experience in software development, machine learning, and cybersecurity through innovative projects and competitive programming.</p>
      </div><!-- End Section Title -->

      <div class="container">

        <div class="row">

          <div class="col-lg-6" data-aos="fade-up" data-aos-delay="100">
            <h3 class="resume-title">Summary</h3>

            <div class="resume-item pb-0">
              <h4>Surendar Kumar</h4>
              <p><em>Passionate Computer Science Engineering student with expertise in software development, machine learning, and cybersecurity. Proven track record in building innovative solutions and participating in competitive hackathons at state and national levels.</em></p>
              <ul>
                <li>Chikmagalur, Karnataka, India</li>
                <li>+91-8971291369</li>
                <li><EMAIL></li>
              </ul>
            </div><!-- End Resume Item -->

            <h3 class="resume-title">Education</h3>
            <div class="resume-item">
              <h4>Bachelor of Engineering - Computer Science and Engineering</h4>
              <h5>December 2022 - April 2026</h5>
              <p><em>Adichunchanagiri Institute Of Technology, Chikmagalur, India</em></p>
              <p>Currently pursuing B.E. in Computer Science with an outstanding GPA of 9.1. Focused on software development, machine learning, and cybersecurity with hands-on project experience.</p>
            </div><!-- End Resume Item -->

            <div class="resume-item">
              <h4>Pre-University Course (PCMC)</h4>
              <h5>June 2020 - April 2022</h5>
              <p><em>St. Mary's PU College, Chikmagalur, Karnataka</em></p>
              <p>Completed Pre-University education with an exceptional percentage of 96.8% in Physics, Chemistry, Mathematics, and Computer Science, laying a strong foundation for engineering studies.</p>
            </div><!-- End Resume Item -->

            <div class="resume-item">
              <h4>Secondary School Certificate (10th Grade)</h4>
              <h5>June 2019 - April 2020</h5>
              <p><em>St. Mary's International School, Chikmagalur, Karnataka</em></p>
              <p>Achieved 92.6% in secondary education, demonstrating consistent academic excellence and strong fundamentals in core subjects.</p>
            </div><!-- End Resume Item -->

          </div>

          <div class="col-lg-6" data-aos="fade-up" data-aos-delay="200">
            <h3 class="resume-title">Certifications & Achievements</h3>
            <div class="resume-item">
              <h4>Professional Certifications</h4>
              <h5>2025</h5>
              <p><em>Multiple Industry-Recognized Certifications</em></p>
              <ul>
                <li>NPTEL Programming in Java (Jan-Apr 2025)</li>
                <li>NPTEL Machine Learning (Jan-Mar 2025)</li>
                <li>Academic Process Mining Fundamentals - Celonis (Apr 2025)</li>
                <li>AWS Academy Graduate - Cloud Architecting (Jul 2025)</li>
              </ul>
            </div><!-- End Resume Item -->

            <div class="resume-item">
              <h4>Hackathon Achievements</h4>
              <h5>2024 - 2025</h5>
              <p><em>State and National Level Competitions</em></p>
              <ul>
                <li>hackCSE|erate - Adichunchanagiri Institute of Technology (24-hour national-level hackathon)</li>
                <li>State-Level Hackathon - Malnad College of Engineering (Recognized for innovative contributions)</li>
                <li>Tech Tank 2025 - RV College of Engineering, Bengaluru (Selected for Round 2)</li>
                <li>Sentinel Hack 5.0 - K.S. Institute of Technology (AI and cybersecurity focus)</li>
                <li>IGNITEX 2025 - Adichunchanagiri University (National-level software and hardware hackathon)</li>
              </ul>
            </div><!-- End Resume Item -->

          </div>

        </div>

      </div>

    </section><!-- /Resume Section -->

    <!-- Badges, Certificates & Projects Section -->
    <section id="portfolio" class="portfolio section light-background">

      <!-- Badges Section -->
      <div class="container" data-aos="fade-up">
        <div class="section-title text-center mb-5">
          <h2>Badges</h2>
          <p>Professional achievement badges and certifications from industry-leading platforms</p>
        </div>

        <div class="row gy-4 justify-content-center mb-5">
          <div class="col-lg-4 col-md-6 col-sm-6">
            <div class="badge-card">
              <div class="badge-icon">
                <img src="assets/img/badges/aws-cloud-architecting.svg" alt="AWS Badge" class="badge-img">
              </div>
              <h5>AWS Cloud Architecting</h5>
              <p class="badge-desc">AWS Academy Cloud Architecting certification badge for platform discovery</p>
            </div>
          </div>

          <div class="col-lg-4 col-md-6 col-sm-6">
            <div class="badge-card">
              <div class="badge-icon">
                <img src="assets/img/badges/celonis-process-mining.svg" alt="Celonis Badge" class="badge-img">
              </div>
              <h5>Process Mining Fundamentals</h5>
              <p class="badge-desc">Celonis Academic Process Mining Fundamentals badge for exploring the platform</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Certificates Section -->
      <div class="container" data-aos="fade-up">
        <div class="section-title text-center mb-5">
          <h2>Certificates</h2>
          <p>Professional certifications demonstrating expertise across multiple domains</p>
        </div>

        <div class="row gy-4 mb-5">
          <div class="col-lg-6">
            <div class="certificate-card">
              <h4>NPTEL Programming in Java</h4>
              <p class="cert-issuer">Issued by NPTEL • Jan-Apr 2025</p>
              <p class="cert-id">Credential ID: NPTEL25CS57S11434I00074</p>
              <a href="#" class="cert-link"><i class="bi bi-link-45deg"></i> View Certificate</a>
            </div>
          </div>

          <div class="col-lg-6">
            <div class="certificate-card">
              <h4>NPTEL Machine Learning</h4>
              <p class="cert-issuer">Issued by NPTEL • Jan-Mar 2025</p>
              <p class="cert-id">Credential ID: NPTEL25CS50S43660015B</p>
              <a href="#" class="cert-link"><i class="bi bi-link-45deg"></i> View Certificate</a>
            </div>
          </div>

          <div class="col-lg-6">
            <div class="certificate-card">
              <h4>Academic Process Mining Fundamentals</h4>
              <p class="cert-issuer">Issued by Celonis • Apr 2025</p>
              <p class="cert-id">Credential ID: 167b9c8f-7334-47a1-81c4-d828e90d215c</p>
              <a href="https://www.credly.com/badges/167b9c8f-7334-47a1-81c4-d828e90d215c" target="_blank" class="cert-link"><i class="bi bi-link-45deg"></i> View Certificate</a>
            </div>
          </div>

          <div class="col-lg-6">
            <div class="certificate-card">
              <h4>AWS Academy Graduate - Cloud Architecting</h4>
              <p class="cert-issuer">Issued by AWS Academy • Jul 2025</p>
              <p class="cert-id">Credential ID: AWS-Academy-Graduate</p>
              <a href="https://credly.com/go/7oecHgny" target="_blank" class="cert-link"><i class="bi bi-link-45deg"></i> View Certificate</a>
            </div>
          </div>
        </div>
      </div>

      <!-- Projects Section -->
      <div class="container" data-aos="fade-up">
        <div class="section-title text-center mb-5">
          <h2>Projects</h2>
          <p>Innovative software projects demonstrating expertise in trading algorithms, e-learning platforms, cybersecurity tools, and AI-powered applications</p>
        </div>

        <div class="row gy-4">
          <div class="col-lg-6">
            <div class="project-card">
              <h4>Binary Bot -- Binary Trading Signal Generator</h4>
              <p class="project-subtitle">Real-Time Market Analysis, Trading Algorithms, Notification System</p>
              <p class="project-desc">Real-time binary trading signal generator supporting 10+ currency pairs, using Smart Money Concepts (SMC), RSI, and volume-based strategies on 5-minute candles. Generates actionable signals with pop-up notifications and allows external strategy integration. Includes a 6-month backtesting engine.</p>
              <p class="project-tech">Python • Flask • JavaScript • TradingView API • TailwindCSS</p>
              <a href="#" class="project-link"><i class="bi bi-github"></i> GitHub →</a>
            </div>
          </div>

          <div class="col-lg-6">
            <div class="project-card">
              <h4>E-Shiksha -- Competitive Training Web App</h4>
              <p class="project-subtitle">E-Learning, Gamification, Performance Tracking</p>
              <p class="project-desc">Interactive platform offering quizzes, mock tests, progress tracking, and study material access tailored for competitive exams. Features user authentication, leaderboard rankings, and AI-based question suggestions for personalized learning paths.</p>
              <p class="project-tech">ReactJS • Node.js • MongoDB • TailwindCSS</p>
              <a href="#" class="project-link"><i class="bi bi-github"></i> GitHub →</a>
            </div>
          </div>

          <div class="col-lg-6">
            <div class="project-card">
              <h4>InstaScan -- Food Content Recognition & Calorie Calculator</h4>
              <p class="project-subtitle">Image Processing, Nutrition AI, Chatbot Integration</p>
              <p class="project-desc">Mobile/web app that scans food product labels via images to extract ingredients, display nutritional information, and estimate calorie content. Includes a conversational chatbot for health tips, meal tracking, and diet recommendations.</p>
              <p class="project-tech">Python • OpenCV • Flask • ReactJS • Dialogflow</p>
              <a href="#" class="project-link"><i class="bi bi-github"></i> GitHub →</a>
            </div>
          </div>

          <div class="col-lg-6">
            <div class="project-card">
              <h4>PhishShield -- Phishing Website Detection Tool</h4>
              <p class="project-subtitle">Cybersecurity, Web Scraping, Permutation Analysis</p>
              <p class="project-desc">Detects phishing domains using DNS permutation-based analysis, WHOIS checks, and SSL certificate validation. Built-in URL similarity scoring helps identify impersonation. Web interface supports live URL checks and blacklist integration.</p>
              <p class="project-tech">Python • Flask • DnsTwist • OpenSSL • ReactJS</p>
              <a href="#" class="project-link"><i class="bi bi-github"></i> GitHub →</a>
            </div>
          </div>
        </div>
      </div>

    </section><!-- /Badges, Certificates & Projects Section -->

    <!-- Services Section -->
    <section id="services" class="services section">

      <!-- Section Title -->
      <div class="container section-title" data-aos="fade-up">
        <h2>Services</h2>
        <p>Comprehensive software development services specializing in web applications, machine learning solutions, cybersecurity tools, and trading algorithms. Delivering innovative solutions with cutting-edge technologies.</p>
      </div><!-- End Section Title -->

      <div class="container">

        <div class="row gy-4">

          <div class="col-lg-4 col-md-6 service-item d-flex" data-aos="fade-up" data-aos-delay="100">
            <div class="icon flex-shrink-0"><i class="bi bi-code-slash"></i></div>
            <div>
              <h4 class="title"><a href="service-details.html" class="stretched-link">Web Development</a></h4>
              <p class="description">Full-stack web application development using modern frameworks like React, Flask, and Node.js with responsive design and optimal performance</p>
            </div>
          </div>
          <!-- End Service Item -->

          <div class="col-lg-4 col-md-6 service-item d-flex" data-aos="fade-up" data-aos-delay="200">
            <div class="icon flex-shrink-0"><i class="bi bi-robot"></i></div>
            <div>
              <h4 class="title"><a href="service-details.html" class="stretched-link">Machine Learning Solutions</a></h4>
              <p class="description">AI-powered applications including image processing, natural language processing, and predictive analytics for business intelligence</p>
            </div>
          </div><!-- End Service Item -->

          <div class="col-lg-4 col-md-6 service-item d-flex" data-aos="fade-up" data-aos-delay="300">
            <div class="icon flex-shrink-0"><i class="bi bi-shield-check"></i></div>
            <div>
              <h4 class="title"><a href="service-details.html" class="stretched-link">Cybersecurity Tools</a></h4>
              <p class="description">Security analysis tools, phishing detection systems, and vulnerability assessment solutions to protect digital assets</p>
            </div>
          </div><!-- End Service Item -->

          <div class="col-lg-4 col-md-6 service-item d-flex" data-aos="fade-up" data-aos-delay="400">
            <div class="icon flex-shrink-0"><i class="bi bi-graph-up-arrow"></i></div>
            <div>
              <h4 class="title"><a href="service-details.html" class="stretched-link">Trading Algorithms</a></h4>
              <p class="description">Automated trading systems with real-time signal generation, backtesting capabilities, and Smart Money Concepts integration</p>
            </div>
          </div><!-- End Service Item -->

          <div class="col-lg-4 col-md-6 service-item d-flex" data-aos="fade-up" data-aos-delay="500">
            <div class="icon flex-shrink-0"><i class="bi bi-database"></i></div>
            <div>
              <h4 class="title"><a href="service-details.html" class="stretched-link">Database Solutions</a></h4>
              <p class="description">Database design, optimization, and management with MySQL, including data migration and performance tuning services</p>
            </div>
          </div><!-- End Service Item -->

          <div class="col-lg-4 col-md-6 service-item d-flex" data-aos="fade-up" data-aos-delay="600">
            <div class="icon flex-shrink-0"><i class="bi bi-laptop"></i></div>
            <div>
              <h4 class="title"><a href="service-details.html" class="stretched-link">Software Consulting</a></h4>
              <p class="description">Technical consulting for software architecture, code review, performance optimization, and technology stack selection</p>
            </div>
          </div><!-- End Service Item -->

        </div>

      </div>

    </section><!-- /Services Section -->





    <!-- Contact Section -->
    <section id="contact" class="contact section">

      <!-- Section Title -->
      <div class="container section-title" data-aos="fade-up">
        <h2>Contact</h2>
        <p>Ready to collaborate on innovative software solutions? Let's connect and discuss how we can bring your ideas to life with cutting-edge technology and creative problem-solving.</p>
      </div><!-- End Section Title -->

      <div class="container" data-aos="fade-up" data-aos-delay="100">

        <div class="row gy-4">

          <div class="col-lg-5">

            <div class="info-wrap">
              <div class="info-item d-flex" data-aos="fade-up" data-aos-delay="200">
                <i class="bi bi-geo-alt flex-shrink-0"></i>
                <div>
                  <h3>Location</h3>
                  <p>Chikmagalur, Karnataka, India</p>
                </div>
              </div><!-- End Info Item -->

              <div class="info-item d-flex" data-aos="fade-up" data-aos-delay="300">
                <i class="bi bi-telephone flex-shrink-0"></i>
                <div>
                  <h3>Call Me</h3>
                  <p>+91-8971291369</p>
                </div>
              </div><!-- End Info Item -->

              <div class="info-item d-flex" data-aos="fade-up" data-aos-delay="400">
                <i class="bi bi-envelope flex-shrink-0"></i>
                <div>
                  <h3>Email Me</h3>
                  <p><a href="mailto:<EMAIL>"><EMAIL></a></p>
                </div>
              </div><!-- End Info Item -->

              <iframe src="https://www.google.com/maps/embed?pb=!1m14!1m8!1m3!1d48389.78314118045!2d-74.006138!3d40.710059!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x89c25a22a3bda30d%3A0xb89d1fe6bc499443!2sDowntown%20Conference%20Center!5e0!3m2!1sen!2sus!4v1676961268712!5m2!1sen!2sus" frameborder="0" style="border:0; width: 100%; height: 270px;" allowfullscreen="" loading="lazy" referrerpolicy="no-referrer-when-downgrade"></iframe>
            </div>
          </div>

          <div class="col-lg-7">
            <form action="forms/contact.php" method="post" class="php-email-form" data-aos="fade-up" data-aos-delay="200">
              <div class="row gy-4">

                <div class="col-md-6">
                  <label for="name-field" class="pb-2">Your Name</label>
                  <input type="text" name="name" id="name-field" class="form-control" required="">
                </div>

                <div class="col-md-6">
                  <label for="email-field" class="pb-2">Your Email</label>
                  <input type="email" class="form-control" name="email" id="email-field" required="">
                </div>

                <div class="col-md-12">
                  <label for="subject-field" class="pb-2">Subject</label>
                  <input type="text" class="form-control" name="subject" id="subject-field" required="">
                </div>

                <div class="col-md-12">
                  <label for="message-field" class="pb-2">Message</label>
                  <textarea class="form-control" name="message" rows="10" id="message-field" required=""></textarea>
                </div>

                <div class="col-md-12 text-center">
                  <div class="loading">Loading</div>
                  <div class="error-message"></div>
                  <div class="sent-message">Your message has been sent. Thank you!</div>

                  <button type="submit">Send Message</button>
                </div>

              </div>
            </form>
          </div><!-- End Contact Form -->

        </div>

      </div>

    </section><!-- /Contact Section -->

  </main>

  <!-- Footer -->
  <footer class="shreyanka-footer">
    <div class="container">
      <div class="footer-content">
        <div class="footer-info">
          <h3 class="footer-name">Shreyanka A Y</h3>
          <p class="footer-title">Final Year Student</p>
        </div>

        <div class="footer-social">
          <a href="https://github.com/SurendarKumar3" target="_blank" rel="noopener noreferrer" class="footer-social-link">
            <i class="bi bi-github"></i>
            <span class="sr-only">GitHub</span>
          </a>
          <a href="https://linkedin.com/in/surendar-kumar03/" target="_blank" rel="noopener noreferrer" class="footer-social-link">
            <i class="bi bi-linkedin"></i>
            <span class="sr-only">LinkedIn</span>
          </a>
          <a href="https://www.salesforce.com/trailblazer/profile" target="_blank" rel="noopener noreferrer" class="footer-social-link">
            <i class="bi bi-cloud"></i>
            <span class="sr-only">Salesforce Trailblazer</span>
          </a>
          <a href="mailto:<EMAIL>" class="footer-social-link">
            <i class="bi bi-envelope"></i>
            <span class="sr-only">Email</span>
          </a>
        </div>

        <div class="footer-copyright">
          <p>© 2025 Surendar Kumar. All rights reserved.</p>
        </div>
      </div>
    </div>
  </footer>

  <!-- Scroll Top -->
  <a href="#" id="scroll-top" class="scroll-top d-flex align-items-center justify-content-center"><i class="bi bi-arrow-up-short"></i></a>

  <!-- Preloader -->
  <div id="preloader"></div>

  <!-- Vendor JS Files -->
  <script src="assets/vendor/bootstrap/js/bootstrap.bundle.min.js"></script>
  <script src="assets/vendor/php-email-form/validate.js"></script>
  <script src="assets/vendor/aos/aos.js"></script>
  <script src="assets/vendor/typed.js/typed.umd.js"></script>
  <script src="assets/vendor/purecounter/purecounter_vanilla.js"></script>
  <script src="assets/vendor/waypoints/noframework.waypoints.js"></script>
  <script src="assets/vendor/glightbox/js/glightbox.min.js"></script>
  <script src="assets/vendor/imagesloaded/imagesloaded.pkgd.min.js"></script>
  <script src="assets/vendor/isotope-layout/isotope.pkgd.min.js"></script>
  <script src="assets/vendor/swiper/swiper-bundle.min.js"></script>

  <!-- Main JS File -->
  <script src="assets/js/main.js"></script>

  <!-- Theme Toggle Script -->
  <script>
    // Theme Toggle Functionality
    document.addEventListener('DOMContentLoaded', function() {
      const themeToggle = document.getElementById('theme-toggle');
      const themeIcon = document.getElementById('theme-icon');
      const body = document.body;

      // Check for saved theme preference or default to dark theme
      const currentTheme = localStorage.getItem('theme') || 'dark';

      // Apply the saved theme
      if (currentTheme === 'light') {
        body.classList.add('light-theme');
        themeIcon.className = 'bi bi-moon-fill';
      } else {
        body.classList.remove('light-theme');
        themeIcon.className = 'bi bi-sun-fill';
      }

      // Theme toggle event listener
      themeToggle.addEventListener('click', function() {
        if (body.classList.contains('light-theme')) {
          // Switch to dark theme
          body.classList.remove('light-theme');
          themeIcon.className = 'bi bi-sun-fill';
          localStorage.setItem('theme', 'dark');
        } else {
          // Switch to light theme
          body.classList.add('light-theme');
          themeIcon.className = 'bi bi-moon-fill';
          localStorage.setItem('theme', 'light');
        }
      });
    });
  </script>

</body>

</html>