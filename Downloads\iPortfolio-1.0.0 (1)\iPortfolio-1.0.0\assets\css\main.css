/**
* Template Name: iPortfolio
* Template URL: https://bootstrapmade.com/iportfolio-bootstrap-portfolio-websites-template/
* Updated: Jun 29 2024 with Bootstrap v5.3.3
* Author: BootstrapMade.com
* License: https://bootstrapmade.com/license/
*/

/*--------------------------------------------------------------
# Font & Color Variables
# Help: https://bootstrapmade.com/color-system/
--------------------------------------------------------------*/
/* Fonts */
:root {
  --default-font: "Roboto",  system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", "Liberation Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  --heading-font: "Raleway",  sans-serif;
  --nav-font: "Poppins",  sans-serif;
}

/* Global Colors - The following color variables are used throughout the website. Updating them here will change the color scheme of the entire website */
:root {
  --background-color: #000000; /* Background color for the entire website, including individual sections */
  --default-color: #ffffff; /* Default color used for the majority of the text content across the entire website */
  --heading-color: #ffffff; /* Color for headings, subheadings and title throughout the website */
  --accent-color: #333333; /* Accent color that represents your brand on the website. It's used for buttons, links, and other elements that need to stand out */
  --surface-color: #1a1a1a; /* The surface color is used as a background of boxed elements within sections, such as cards, icon boxes, or other elements that require a visual separation from the global background. */
  --contrast-color: #ffffff; /* Contrast color for text, ensuring readability against backgrounds of accent, heading, or default colors. */
}

/* Nav Menu Colors - The following color variables are used specifically for the navigation menu. They are separate from the global colors to allow for more customization options */
:root {
  --nav-color: #cccccc;  /* The default color of the main navmenu links */
  --nav-hover-color: #ffffff; /* Applied to main navmenu links when they are hovered over or active */
  --nav-mobile-background-color: #000000; /* Used as the background color for mobile navigation menu */
  --nav-dropdown-background-color: #000000; /* Used as the background color for dropdown items that appear when hovering over primary navigation items */
  --nav-dropdown-color: #cccccc; /* Used for navigation links of the dropdown items in the navigation menu. */
  --nav-dropdown-hover-color: #ffffff; /* Similar to --nav-hover-color, this color is applied to dropdown navigation links when they are hovered over. */
}

/* Color Presets - These classes override global colors when applied to any section or element, providing reuse of the sam color scheme. */

.light-background {
  --background-color: #1a1a1a;
  --surface-color: #2a2a2a;
}

.dark-background {
  --background-color: #000000;
  --default-color: #ffffff;
  --heading-color: #ffffff;
  --surface-color: #1a1a1a;
  --contrast-color: #ffffff;
}

/* Light Theme Variables */
body.light-theme {
  --background-color: #ffffff;
  --default-color: #333333;
  --heading-color: #222222;
  --accent-color: #666666;
  --surface-color: #f8f9fa;
  --contrast-color: #ffffff;
  --nav-color: #666666;
  --nav-hover-color: #333333;
  --nav-mobile-background-color: #ffffff;
  --nav-dropdown-background-color: #ffffff;
  --nav-dropdown-color: #666666;
  --nav-dropdown-hover-color: #333333;
}

/* Light Theme Specific Overrides */
body.light-theme .header {
  background-color: #ffffff !important;
  border-right: 1px solid #e9ecef;
}

body.light-theme .badge-card {
  background: #ffffff;
  border: 1px solid #e9ecef;
  color: #272829;
}

body.light-theme .badge-card h5 {
  color: #050d18;
}

body.light-theme .badge-desc {
  color: #666666;
}

body.light-theme .certificate-card {
  background: #ffffff;
  border: 1px solid #e9ecef;
}

body.light-theme .certificate-card h4 {
  color: #050d18;
}

body.light-theme .project-card {
  background: #ffffff;
  border: 1px solid #e9ecef;
}

body.light-theme .project-card h4 {
  color: #050d18;
}

body.light-theme .project-desc {
  color: #666666;
}

body.light-theme .hero {
  background-color: #f8f9fa !important;
}

body.light-theme .section {
  background-color: var(--background-color);
}

body.light-theme .light-background {
  background-color: #f8f9fa;
}

body.light-theme .resume-download-section {
  background: linear-gradient(135deg, #e9ecef, #dee2e6);
}

body.light-theme .resume-download-card h3 {
  color: #333333;
  text-shadow: none;
}

body.light-theme .resume-download-card p {
  color: #666666;
  text-shadow: none;
}

body.light-theme .resume-download-card {
  background: rgba(255, 255, 255, 0.9);
  border: 2px solid rgba(0, 0, 0, 0.1);
}

body.light-theme .resume-download-btn {
  background: #333333;
  color: #ffffff;
  border-color: #555555;
}

body.light-theme .resume-download-btn:hover {
  background: #555555;
  border-color: #777777;
}

/* Smooth scroll */
:root {
  scroll-behavior: smooth;
}

/*--------------------------------------------------------------
# General Styling & Shared Classes
--------------------------------------------------------------*/
body {
  color: var(--default-color);
  background-color: var(--background-color);
  font-family: var(--default-font);
}

a {
  color: var(--accent-color);
  text-decoration: none;
  transition: 0.3s;
}

a:hover {
  color: color-mix(in srgb, var(--accent-color), transparent 25%);
  text-decoration: none;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  color: var(--heading-color);
  font-family: var(--heading-font);
}

/* PHP Email Form Messages
------------------------------*/
.php-email-form .error-message {
  display: none;
  background: #df1529;
  color: #ffffff;
  text-align: left;
  padding: 15px;
  margin-bottom: 24px;
  font-weight: 600;
}

.php-email-form .sent-message {
  display: none;
  color: #ffffff;
  background: #059652;
  text-align: center;
  padding: 15px;
  margin-bottom: 24px;
  font-weight: 600;
}

.php-email-form .loading {
  display: none;
  background: var(--surface-color);
  text-align: center;
  padding: 15px;
  margin-bottom: 24px;
}

.php-email-form .loading:before {
  content: "";
  display: inline-block;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  margin: 0 10px -6px 0;
  border: 3px solid var(--accent-color);
  border-top-color: var(--surface-color);
  animation: php-email-form-loading 1s linear infinite;
}

@keyframes php-email-form-loading {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/*--------------------------------------------------------------
# Global Header
--------------------------------------------------------------*/
.header {
  display: none !important;
}

.header .profile-img img {
  margin: 15px auto;
  display: block;
  width: 120px;
  border: 8px solid color-mix(in srgb, var(--default-color), transparent 85%);
}

.header .logo {
  line-height: 1;
  margin-bottom: 15px;
}

.header .logo img {
  max-height: 32px;
  margin-right: 8px;
}

.header .logo h1 {
  font-size: 24px;
  margin: 0;
  font-weight: 700;
  color: var(--heading-color);
}

.header .social-links {
  margin: 0 0 20px 0;
}

.header .social-links a {
  font-size: 16px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background: color-mix(in srgb, var(--default-color), transparent 90%);
  color: var(--default-color);
  margin: 0 2px;
  border-radius: 50%;
  text-align: center;
  width: 40px;
  height: 40px;
  transition: 0.3s;
}

.header .social-links a:hover {
  color: var(--contrast-color);
  background: var(--accent-color);
}

@media (min-width: 1200px) {

  .header~main,
  .header~#footer {
    margin-left: 0px;
  }
}

@media (max-width: 1199px) {
  .header {
    left: -100%;
  }
}

.header.header-show {
  left: 0;
}

.header .header-toggle {
  color: var(--contrast-color);
  background-color: var(--accent-color);
  font-size: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  cursor: pointer;
  position: fixed;
  top: 15px;
  right: 15px;
  z-index: 9999;
  transition: background-color 0.3s;
}

.header .header-toggle:hover {
  color: var(--contrast-color);
  background-color: color-mix(in srgb, var(--accent-color) 90%, white 15%);
}

/*--------------------------------------------------------------
# Navigation Menu
--------------------------------------------------------------*/
.mobile-nav-toggle {
  display: none;
}

.navmenu {
  padding: 0;
  z-index: 9997;
}

.navmenu ul {
  list-style: none;
  padding: 0 0 20px 0;
  margin: 0;
}

.navmenu a,
.navmenu a:focus {
  color: var(--nav-color);
  padding: 15px 10px;
  font-family: var(--nav-font);
  font-size: 16px;
  font-weight: 400;
  display: flex;
  align-items: center;
  white-space: nowrap;
  transition: 0.3s;
  width: 100%;
}

.navmenu a .navicon,
.navmenu a:focus .navicon {
  font-size: 20px;
  margin-right: 10px;
}

.navmenu a .toggle-dropdown,
.navmenu a:focus .toggle-dropdown {
  font-size: 12px;
  line-height: 0;
  margin-left: auto;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: 0.3s;
  flex-shrink: 0;
  background-color: color-mix(in srgb, var(--accent-color), transparent 90%);
}

.navmenu a .toggle-dropdown:hover,
.navmenu a:focus .toggle-dropdown:hover {
  background-color: var(--accent-color);
  color: var(--contrast-color);
}

.navmenu a:hover,
.navmenu .active,
.navmenu .active:focus {
  color: var(--nav-hover-color);
}

.navmenu a:hover .navicon,
.navmenu .active .navicon,
.navmenu .active:focus .navicon {
  color: var(--accent-color);
}

.navmenu .active .toggle-dropdown,
.navmenu .active:focus .toggle-dropdown {
  background-color: var(--accent-color);
  color: var(--contrast-color);
  transform: rotate(180deg);
}

.navmenu .dropdown {
  display: block;
}

.navmenu .dropdown a,
.navmenu .dropdown a:focus {
  color: --nav-dropdown-color;
}

.navmenu .dropdown a:hover,
.navmenu .dropdown .active,
.navmenu .dropdown .active:focus {
  color: var(--nav-dropdown-hover-color);
}

.navmenu .dropdown ul {
  position: static;
  display: none;
  z-index: 99;
  padding: 5px 10px;
  margin: 5px 10px;
  background-color: var(--nav-dropdown-background-color);
  border: 1px solid color-mix(in srgb, var(--default-color), transparent 90%);
  box-shadow: none;
  transition: all 0.5s ease-in-out;
}

.navmenu .dropdown ul ul {
  background-color: rgba(33, 37, 41, 0.1);
}

.navmenu .dropdown>.dropdown-active {
  display: block;
  background-color: rgba(33, 37, 41, 0.03);
}

/*--------------------------------------------------------------
# Global Footer
--------------------------------------------------------------*/
.footer {
  color: var(--default-color);
  background-color: var(--background-color);
  font-size: 14px;
  padding: 40px 0;
  position: relative;
  border-top: 1px solid color-mix(in srgb, var(--default-color), transparent 90%);
}

.footer .copyright p {
  margin-bottom: 0;
}

.footer .credits {
  margin-top: 4px;
  font-size: 13px;
  text-align: center;
}

/*--------------------------------------------------------------
# Preloader
--------------------------------------------------------------*/
#preloader {
  position: fixed;
  inset: 0;
  z-index: 999999;
  overflow: hidden;
  background: var(--background-color);
  transition: all 0.6s ease-out;
}

#preloader:before {
  content: "";
  position: fixed;
  top: calc(50% - 30px);
  left: calc(50% - 30px);
  border: 6px solid #ffffff;
  border-color: var(--accent-color) transparent var(--accent-color) transparent;
  border-radius: 50%;
  width: 60px;
  height: 60px;
  animation: animate-preloader 1.5s linear infinite;
}

@keyframes animate-preloader {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/*--------------------------------------------------------------
# Scroll Top Button
--------------------------------------------------------------*/
.scroll-top {
  position: fixed;
  visibility: hidden;
  opacity: 0;
  right: 15px;
  bottom: -15px;
  z-index: 99999;
  background-color: var(--accent-color);
  width: 44px;
  height: 44px;
  border-radius: 50px;
  transition: all 0.4s;
}

.scroll-top i {
  font-size: 24px;
  color: var(--contrast-color);
  line-height: 0;
}

.scroll-top:hover {
  background-color: color-mix(in srgb, var(--accent-color), transparent 20%);
  color: var(--contrast-color);
}

.scroll-top.active {
  visibility: visible;
  opacity: 1;
  bottom: 15px;
}

/*--------------------------------------------------------------
# Disable aos animation delay on mobile devices
--------------------------------------------------------------*/
@media screen and (max-width: 768px) {
  [data-aos-delay] {
    transition-delay: 0 !important;
  }
}

/*--------------------------------------------------------------
# Global Page Titles & Breadcrumbs
--------------------------------------------------------------*/
.page-title {
  color: var(--default-color);
  background-color: var(--background-color);
  padding: 25px 0;
  position: relative;
}

.page-title h1 {
  font-size: 24px;
  font-weight: 700;
}

.page-title .breadcrumbs ol {
  display: flex;
  flex-wrap: wrap;
  list-style: none;
  padding: 0;
  margin: 0;
  font-size: 14px;
  font-weight: 400;
}

.page-title .breadcrumbs ol li+li {
  padding-left: 10px;
}

.page-title .breadcrumbs ol li+li::before {
  content: "/";
  display: inline-block;
  padding-right: 10px;
  color: color-mix(in srgb, var(--default-color), transparent 70%);
}

/*--------------------------------------------------------------
# Global Sections
--------------------------------------------------------------*/
section,
.section {
  color: var(--default-color);
  background-color: var(--background-color);
  padding: 60px 0;
  overflow: clip;
}

/*--------------------------------------------------------------
# Global Section Titles
--------------------------------------------------------------*/
.section-title {
  padding-bottom: 60px;
  position: relative;
}

.section-title h2 {
  font-size: 32px;
  font-weight: 700;
  margin-bottom: 20px;
  padding-bottom: 20px;
  position: relative;
}

.section-title h2:after {
  content: "";
  position: absolute;
  display: block;
  width: 50px;
  height: 3px;
  background: var(--accent-color);
  left: 0;
  bottom: 0;
}

.section-title p {
  margin-bottom: 0;
}

/*--------------------------------------------------------------
# Hero Section
--------------------------------------------------------------*/
.hero {
  width: 100%;
  min-height: 100vh;
  position: relative;
  padding: 80px 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.hero .container {
  position: relative;
  z-index: 3;
}

.hero .row {
  min-height: 80vh;
}

.hero h2 {
  margin: 0;
  font-size: 64px;
  font-weight: 700;
  margin-bottom: 20px;
}

.hero p {
  margin: 5px 0 0 0;
  font-size: 26px;
}

.hero p span {
  letter-spacing: 1px;
  border-bottom: 2px solid var(--accent-color);
}

.hero-image {
  text-align: center;
  position: relative;
}

.hero-image img {
  max-width: 100%;
  height: auto;
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hero-image img:hover {
  transform: translateY(-10px);
  box-shadow: 0 30px 60px rgba(0, 0, 0, 0.4);
}

.hero .social-links {
  margin-top: 25px;
}

.hero .social-links a {
  font-size: 24px;
  display: inline-block;
  color: color-mix(in srgb, var(--default-color), transparent 50%);
  line-height: 1;
  margin-right: 20px;
  transition: 0.3s;
}

.hero .social-links a:hover {
  color: var(--accent-color);
}

/* Responsive Design for Hero Section */
@media (max-width: 992px) {
  .hero h2 {
    font-size: 48px;
  }

  .hero p {
    font-size: 22px;
  }

  .hero .row {
    min-height: auto;
  }

  .hero-image {
    margin-top: 40px;
  }
}

@media (max-width: 768px) {
  .hero {
    padding: 60px 0;
  }

  .hero h2 {
    font-size: 36px;
  }

  .hero p {
    font-size: 18px;
  }

  .hero-image {
    margin-top: 30px;
  }

  .hero-image img {
    border-radius: 15px;
  }
}

@media (max-width: 768px) {
  .hero h2 {
    font-size: 32px;
  }

  .hero p {
    font-size: 20px;
  }
}

/*--------------------------------------------------------------
# About Section
--------------------------------------------------------------*/
.about .content h2 {
  font-weight: 700;
  font-size: 24px;
}

.about .content ul {
  list-style: none;
  padding: 0;
}

.about .content ul li {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
}

.about .content ul strong {
  margin-right: 10px;
}

.about .content ul i {
  font-size: 16px;
  margin-right: 5px;
  color: var(--accent-color);
  line-height: 0;
}

/*--------------------------------------------------------------
# Stats Section
--------------------------------------------------------------*/
.stats .stats-item {
  padding: 30px;
  width: 100%;
}

.stats .stats-item i {
  color: var(--accent-color);
  display: block;
  font-size: 44px;
  float: left;
  line-height: 0;
}

.stats .stats-item .purecounter {
  color: var(--heading-color);
  font-size: 48px;
  line-height: 40px;
  display: block;
  font-weight: 700;
  margin-left: 60px;
}

.stats .stats-item p {
  color: color-mix(in srgb, var(--default-color), transparent 40%);
  padding: 15px 0 0 0;
  margin: 0 0 0 60px;
  font-family: var(--heading-font);
  font-size: 14px;
}

/*--------------------------------------------------------------
# Skills Section
--------------------------------------------------------------*/
.skills .progress {
  height: 60px;
  display: block;
  background: none;
  border-radius: 0;
  margin-bottom: 20px;
}

.skills .progress .skill {
  color: #ffffff;
  padding: 0;
  margin: 0 0 6px 0;
  text-transform: uppercase;
  display: block;
  font-weight: 600;
  font-family: var(--heading-font);
  font-size: 16px;
}

.skills .progress .skill .val {
  float: right;
  font-style: normal;
  color: #f5f5dc;
}

.skills .progress-bar-wrap {
  background: #333333;
  height: 12px;
  border-radius: 6px;
  overflow: hidden;
}

.skills .progress-bar {
  width: 1px;
  height: 12px;
  transition: 0.9s;
  background: linear-gradient(45deg, #666666, #999999);
  border-radius: 6px;
}

/* Light theme overrides for skills */
body.light-theme .skills .progress .skill {
  color: #333333;
}

body.light-theme .skills .progress .skill .val {
  color: #666666;
}

body.light-theme .skills .progress-bar-wrap {
  background: #e9ecef;
}

body.light-theme .skills .progress-bar {
  background: linear-gradient(45deg, #666666, #333333);
}

/*--------------------------------------------------------------
# Resume Section
--------------------------------------------------------------*/
.resume .resume-title {
  color: var(--heading-color);
  font-size: 26px;
  font-weight: 700;
  margin-top: 20px;
  margin-bottom: 20px;
}

.resume .resume-item {
  padding: 0 0 20px 20px;
  margin-top: -2px;
  border-left: 2px solid var(--accent-color);
  position: relative;
}

.resume .resume-item h4 {
  line-height: 18px;
  font-size: 18px;
  font-weight: 600;
  text-transform: uppercase;
  color: color-mix(in srgb, var(--default-color), transparent 20%);
  margin-bottom: 10px;
}

.resume .resume-item h5 {
  font-size: 16px;
  padding: 5px 15px;
  display: inline-block;
  font-weight: 600;
  margin-bottom: 10px;
}

.resume .resume-item ul {
  padding-left: 20px;
}

.resume .resume-item ul li {
  padding-bottom: 10px;
}

.resume .resume-item:last-child {
  padding-bottom: 0;
}

.resume .resume-item::before {
  content: "";
  position: absolute;
  width: 16px;
  height: 16px;
  border-radius: 50px;
  left: -9px;
  top: 0;
  background: var(--background-color);
  border: 2px solid var(--accent-color);
}

/*--------------------------------------------------------------
# Badges, Certificates & Projects Section
--------------------------------------------------------------*/

/* Badge Cards */
.badge-card {
  background: #0a0a0a;
  border: 1px solid #2a2a2a;
  border-radius: 12px;
  padding: 30px 20px;
  text-align: center;
  transition: all 0.3s ease;
  height: 100%;
  min-height: 280px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.badge-card:hover {
  transform: translateY(-5px);
  border-color: #555555;
  box-shadow: 0 10px 30px rgba(255, 255, 255, 0.1);
}

.badge-icon {
  margin-bottom: 20px;
}

.badge-img {
  width: 80px;
  height: 80px;
  object-fit: contain;
  border-radius: 8px;
}

.badge-card h5 {
  color: #fff;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 15px;
  line-height: 1.3;
}

.badge-desc {
  color: #aaa;
  font-size: 14px;
  line-height: 1.5;
  margin: 0;
}

/* Certificate Cards */
.certificate-card {
  background: #0a0a0a;
  border: 1px solid #2a2a2a;
  border-radius: 12px;
  padding: 30px;
  transition: all 0.3s ease;
  height: 100%;
  min-height: 200px;
}

.certificate-card:hover {
  transform: translateY(-3px);
  border-color: #555555;
  box-shadow: 0 8px 25px rgba(255, 255, 255, 0.1);
}

.certificate-card h4 {
  color: #fff;
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 15px;
  line-height: 1.3;
}

.cert-issuer {
  color: #007bff;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 10px;
}

.cert-id {
  color: #888;
  font-size: 13px;
  margin-bottom: 20px;
  font-family: 'Courier New', monospace;
}

.cert-link {
  color: var(--accent-color);
  text-decoration: none;
  font-weight: 500;
  font-size: 14px;
  transition: all 0.3s ease;
}

.cert-link:hover {
  color: #fff;
  text-decoration: underline;
}

.cert-link i {
  margin-right: 5px;
}

/* Project Cards */
.project-card {
  background: #0a0a0a;
  border: 1px solid #2a2a2a;
  border-radius: 12px;
  padding: 30px;
  transition: all 0.3s ease;
  height: 100%;
  min-height: 320px;
  margin-bottom: 30px;
}

.project-card:hover {
  transform: translateY(-3px);
  border-color: #555555;
  box-shadow: 0 8px 25px rgba(255, 255, 255, 0.1);
}

.project-card h4 {
  color: #fff;
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 10px;
  line-height: 1.3;
}

.project-subtitle {
  color: var(--accent-color);
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 15px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.project-desc {
  color: #ccc;
  font-size: 15px;
  line-height: 1.6;
  margin-bottom: 20px;
}

.project-tech {
  color: #888;
  font-size: 14px;
  margin-bottom: 20px;
  font-weight: 500;
}

.project-link {
  color: var(--accent-color);
  text-decoration: none;
  font-weight: 500;
  font-size: 14px;
  transition: all 0.3s ease;
}

.project-link:hover {
  color: #fff;
  text-decoration: underline;
}

.project-link i {
  margin-right: 5px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .badge-card, .certificate-card, .project-card {
    margin-bottom: 20px;
  }

  .badge-card {
    min-height: 250px;
    padding: 25px 15px;
  }

  .certificate-card {
    min-height: 180px;
    padding: 25px;
  }

  .project-card {
    min-height: 280px;
    padding: 25px;
  }
}

/*--------------------------------------------------------------
# Theme Toggle Button
--------------------------------------------------------------*/
.theme-toggle-container {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 1000;
}

.theme-toggle-btn {
  background: var(--accent-color);
  border: none;
  border-radius: 50%;
  width: 45px;
  height: 45px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.theme-toggle-btn:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.theme-toggle-btn i {
  color: var(--contrast-color);
  font-size: 20px;
  transition: transform 0.3s ease;
}

.theme-toggle-btn:hover i {
  transform: rotate(180deg);
}

/*--------------------------------------------------------------
# Resume Download Section
--------------------------------------------------------------*/
.resume-download-section {
  padding: 60px 0;
  background: linear-gradient(135deg, #1a1a1a, #333333);
  position: relative;
  overflow: hidden;
}

.resume-download-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.1;
}

.resume-download-card {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 20px;
  padding: 40px;
  text-align: center;
  position: relative;
  z-index: 1;
  max-width: 500px;
  margin: 0 auto;
}

.resume-download-card h3 {
  color: #ffffff;
  font-size: 28px;
  font-weight: 700;
  margin-bottom: 15px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.resume-download-card p {
  color: #f5f5dc;
  font-size: 16px;
  margin-bottom: 30px;
  line-height: 1.6;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.resume-download-btn {
  display: inline-flex;
  align-items: center;
  gap: 10px;
  background: #000000;
  color: #ffffff;
  padding: 15px 30px;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 600;
  font-size: 16px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  border: 2px solid #333333;
  width: 3cm;
  height: 3cm;
  justify-content: center;
  flex-direction: column;
  text-align: center;
}

.resume-download-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
  color: #ffffff;
  text-decoration: none;
  background: #1a1a1a;
  border-color: #555555;
}

.resume-download-btn i {
  font-size: 24px;
  margin-bottom: 8px;
}

.resume-download-btn span {
  font-size: 14px;
  line-height: 1.2;
  font-weight: 600;
}

.resume-download-btn i {
  font-size: 18px;
}

@media (max-width: 768px) {
  .resume-download-section {
    padding: 40px 0;
  }

  .resume-download-card {
    padding: 30px 20px;
    margin: 0 20px;
  }

  .resume-download-card h3 {
    font-size: 24px;
  }

  .theme-toggle-container {
    top: 15px;
    right: 15px;
  }

  .theme-toggle-btn {
    width: 40px;
    height: 40px;
  }

  .theme-toggle-btn i {
    font-size: 18px;
  }
}

/*--------------------------------------------------------------
# Services Section
--------------------------------------------------------------*/
.services .service-item {
  position: relative;
}

.services .service-item .icon {
  background: var(--accent-color);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 54px;
  height: 54px;
  border-radius: 50%;
  transition: 0.5s;
  border: 1px solid var(--accent-color);
  margin-right: 20px;
}

.services .service-item .icon i {
  color: var(--contrast-color);
  font-size: 24px;
  line-height: 0;
}

.services .service-item:hover .icon {
  background: var(--surface-color);
}

.services .service-item:hover .icon i {
  color: var(--accent-color);
}

.services .service-item .title {
  font-weight: 700;
  margin-bottom: 15px;
  font-size: 18px;
}

.services .service-item .title a {
  color: var(--heading-color);
}

.services .service-item .title a:hover {
  color: var(--accent-color);
}

.services .service-item .description {
  line-height: 24px;
  font-size: 14px;
}

/*--------------------------------------------------------------
# Testimonials Section
--------------------------------------------------------------*/
.testimonials .testimonials-carousel,
.testimonials .testimonials-slider {
  overflow: hidden;
}

.testimonials .testimonial-item {
  box-sizing: content-box;
  text-align: center;
  min-height: 320px;
}

.testimonials .testimonial-item .testimonial-img {
  width: 90px;
  border-radius: 50%;
  margin: 0 auto;
}

.testimonials .testimonial-item h3 {
  font-size: 18px;
  font-weight: bold;
  margin: 10px 0 5px 0;
}

.testimonials .testimonial-item h4 {
  font-size: 14px;
  color: color-mix(in srgb, var(--default-color), transparent 20%);
  margin: 0;
}

.testimonials .testimonial-item .quote-icon-left,
.testimonials .testimonial-item .quote-icon-right {
  color: color-mix(in srgb, var(--accent-color), transparent 40%);
  font-size: 26px;
  line-height: 0;
}

.testimonials .testimonial-item .quote-icon-left {
  display: inline-block;
  left: -5px;
  position: relative;
}

.testimonials .testimonial-item .quote-icon-right {
  display: inline-block;
  right: -5px;
  position: relative;
  top: 10px;
  transform: scale(-1, -1);
}

.testimonials .testimonial-item p {
  font-style: italic;
  margin: 0 15px 15px 15px;
  padding: 20px;
  background-color: var(--surface-color);
  position: relative;
  margin-bottom: 35px;
  border-radius: 6px;
  box-shadow: 0px 2px 15px rgba(0, 0, 0, 0.1);
}

.testimonials .testimonial-item p::after {
  content: "";
  width: 0;
  height: 0;
  border-top: 20px solid var(--surface-color);
  border-right: 20px solid transparent;
  border-left: 20px solid transparent;
  position: absolute;
  bottom: -20px;
  left: calc(50% - 20px);
}

.testimonials .swiper-wrapper {
  height: auto;
}

.testimonials .swiper-pagination {
  margin-top: 40px;
  position: relative;
}

.testimonials .swiper-pagination .swiper-pagination-bullet {
  width: 12px;
  height: 12px;
  background-color: var(--background-color);
  opacity: 1;
  border: 1px solid var(--accent-color);
}

.testimonials .swiper-pagination .swiper-pagination-bullet-active {
  background-color: var(--accent-color);
}

/*--------------------------------------------------------------
# Contact Section
--------------------------------------------------------------*/
.contact .info-wrap {
  background-color: var(--surface-color);
  box-shadow: 0px 0px 20px rgba(0, 0, 0, 0.1);
  padding: 30px;
}

@media (max-width: 575px) {
  .contact .info-wrap {
    padding: 20px;
  }
}

.contact .info-item {
  margin-bottom: 40px;
}

.contact .info-item i {
  font-size: 20px;
  color: var(--accent-color);
  background: color-mix(in srgb, var(--accent-color), transparent 92%);
  width: 44px;
  height: 44px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50px;
  transition: all 0.3s ease-in-out;
  margin-right: 15px;
}

.contact .info-item h3 {
  padding: 0;
  font-size: 18px;
  font-weight: 700;
  margin-bottom: 5px;
}

.contact .info-item p {
  padding: 0;
  margin-bottom: 0;
  font-size: 14px;
}

.contact .info-item:hover i {
  background: var(--accent-color);
  color: var(--contrast-color);
}

.contact .php-email-form {
  background-color: var(--surface-color);
  height: 100%;
  padding: 30px;
  box-shadow: 0px 0px 20px rgba(0, 0, 0, 0.1);
}

@media (max-width: 575px) {
  .contact .php-email-form {
    padding: 20px;
  }
}

.contact .php-email-form input[type=text],
.contact .php-email-form input[type=email],
.contact .php-email-form textarea {
  font-size: 14px;
  padding: 10px 15px;
  box-shadow: none;
  border-radius: 0;
  color: var(--default-color);
  background-color: var(--surface-color);
  border-color: color-mix(in srgb, var(--default-color), transparent 80%);
}

.contact .php-email-form input[type=text]:focus,
.contact .php-email-form input[type=email]:focus,
.contact .php-email-form textarea:focus {
  border-color: var(--accent-color);
}

.contact .php-email-form input[type=text]::placeholder,
.contact .php-email-form input[type=email]::placeholder,
.contact .php-email-form textarea::placeholder {
  color: color-mix(in srgb, var(--default-color), transparent 70%);
}

.contact .php-email-form button[type=submit] {
  color: var(--contrast-color);
  background: var(--accent-color);
  border: 0;
  padding: 10px 30px;
  transition: 0.4s;
  border-radius: 50px;
}

.contact .php-email-form button[type=submit]:hover {
  background: color-mix(in srgb, var(--accent-color), transparent 25%);
}

/*--------------------------------------------------------------
# Portfolio Details Section
--------------------------------------------------------------*/
.portfolio-details .portfolio-details-slider img {
  width: 100%;
}

.portfolio-details .portfolio-details-slider .swiper-pagination {
  margin-top: 20px;
  position: relative;
}

.portfolio-details .portfolio-details-slider .swiper-pagination .swiper-pagination-bullet {
  width: 12px;
  height: 12px;
  background-color: color-mix(in srgb, var(--default-color), transparent 85%);
  opacity: 1;
}

.portfolio-details .portfolio-details-slider .swiper-pagination .swiper-pagination-bullet-active {
  background-color: var(--accent-color);
}

.portfolio-details .portfolio-info {
  padding: 30px;
  box-shadow: 0px 0 30px rgba(0, 0, 0, 0.1);
}

.portfolio-details .portfolio-info h3 {
  font-size: 22px;
  font-weight: 700;
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid color-mix(in srgb, var(--default-color), transparent 85%);
}

.portfolio-details .portfolio-info ul {
  list-style: none;
  padding: 0;
  font-size: 15px;
}

.portfolio-details .portfolio-info ul li+li {
  margin-top: 10px;
}

.portfolio-details .portfolio-description {
  padding-top: 30px;
}

.portfolio-details .portfolio-description h2 {
  font-size: 26px;
  font-weight: 700;
  margin-bottom: 20px;
}

.portfolio-details .portfolio-description p {
  padding: 0;
  color: color-mix(in srgb, var(--default-color), transparent 30%);
}

/*--------------------------------------------------------------
# Service Details Section
--------------------------------------------------------------*/
.service-details .services-list {
  padding: 10px 30px;
  border: 1px solid color-mix(in srgb, var(--default-color), transparent 90%);
  margin-bottom: 20px;
}

.service-details .services-list a {
  display: block;
  line-height: 1;
  padding: 8px 0 8px 15px;
  border-left: 3px solid color-mix(in srgb, var(--default-color), transparent 70%);
  margin: 20px 0;
  color: color-mix(in srgb, var(--default-color), transparent 20%);
  transition: 0.3s;
}

.service-details .services-list a.active {
  color: var(--heading-color);
  font-weight: 700;
  border-color: var(--accent-color);
}

.service-details .services-list a:hover {
  border-color: var(--accent-color);
}

.service-details .services-img {
  margin-bottom: 20px;
}

.service-details h3 {
  font-size: 26px;
  font-weight: 700;
}

.service-details h4 {
  font-size: 20px;
  font-weight: 700;
}

.service-details p {
  font-size: 15px;
}

.service-details ul {
  list-style: none;
  padding: 0;
  font-size: 15px;
}

.service-details ul li {
  padding: 5px 0;
  display: flex;
  align-items: center;
}

.service-details ul i {
  font-size: 20px;
  margin-right: 8px;
  color: var(--accent-color);
}

/*--------------------------------------------------------------
# Starter Section Section
--------------------------------------------------------------*/
.starter-section {
  /* Add your styles here */
}

/*--------------------------------------------------------------
# Shreyanka Header
--------------------------------------------------------------*/
.shreyanka-header {
  background: rgba(0, 0, 0, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  z-index: 1000;
  transition: all 0.3s ease;
}

.shreyanka-header .navbar {
  padding: 1rem 0;
}

.shreyanka-header .navbar-brand {
  text-decoration: none;
}

.shreyanka-header .brand-name {
  font-size: 24px;
  font-weight: 700;
  color: #ffffff;
  font-style: italic;
  letter-spacing: 1px;
  text-decoration: underline;
  text-decoration-color: #666666;
  text-underline-offset: 4px;
  transition: all 0.3s ease;
}

.shreyanka-header .brand-name:hover {
  color: #cccccc;
  text-decoration-color: #888888;
}

.shreyanka-header .navbar-nav .nav-link {
  color: #ffffff;
  font-weight: 500;
  font-size: 14px;
  padding: 0.5rem 1rem;
  margin: 0 0.25rem;
  border-radius: 6px;
  transition: all 0.3s ease;
  text-decoration: none;
}

.shreyanka-header .navbar-nav .nav-link:hover {
  color: #ffffff;
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-1px);
}

.shreyanka-header .navbar-nav .nav-link.active {
  color: #ffffff;
  background: rgba(255, 255, 255, 0.15);
}

.shreyanka-header .theme-toggle-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-left: 1rem;
}

.shreyanka-header .theme-toggle-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
  transform: scale(1.05);
}

.shreyanka-header .navbar-toggler {
  border: none;
  padding: 0.25rem 0.5rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 6px;
}

.shreyanka-header .navbar-toggler:focus {
  box-shadow: none;
}

.shreyanka-header .navbar-toggler-icon {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.8%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

/* Light theme overrides for header */
body.light-theme .shreyanka-header {
  background: rgba(255, 255, 255, 0.95);
  border-bottom-color: rgba(0, 0, 0, 0.1);
}

body.light-theme .shreyanka-header .brand-name {
  color: #333333;
  text-decoration-color: #cccccc;
}

body.light-theme .shreyanka-header .brand-name:hover {
  color: #555555;
  text-decoration-color: #aaaaaa;
}

body.light-theme .shreyanka-header .navbar-nav .nav-link {
  color: #333333;
}

body.light-theme .shreyanka-header .navbar-nav .nav-link:hover {
  color: #333333;
  background: rgba(0, 0, 0, 0.05);
}

body.light-theme .shreyanka-header .navbar-nav .nav-link.active {
  color: #333333;
  background: rgba(0, 0, 0, 0.1);
}

body.light-theme .shreyanka-header .theme-toggle-btn {
  background: rgba(0, 0, 0, 0.05);
  border-color: rgba(0, 0, 0, 0.1);
  color: #333333;
}

body.light-theme .shreyanka-header .theme-toggle-btn:hover {
  background: rgba(0, 0, 0, 0.1);
  border-color: rgba(0, 0, 0, 0.2);
}

body.light-theme .shreyanka-header .navbar-toggler {
  background: rgba(0, 0, 0, 0.05);
}

body.light-theme .shreyanka-header .navbar-toggler-icon {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%2833, 37, 41, 0.8%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

/*--------------------------------------------------------------
# Shreyanka Footer
--------------------------------------------------------------*/
.shreyanka-footer {
  background: rgba(221, 246, 210, 1);
  padding: 2.5rem 0;
  border-top: 1px solid rgba(180, 200, 170, 0.3);
}

.shreyanka-footer .footer-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  gap: 1.5rem;
}

.shreyanka-footer .footer-info .footer-name {
  font-size: 20px;
  font-weight: 700;
  color: #333333;
  margin-bottom: 0.5rem;
}

.shreyanka-footer .footer-info .footer-title {
  color: #666666;
  font-size: 14px;
  margin: 0;
}

.shreyanka-footer .footer-social {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.shreyanka-footer .footer-social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: rgba(180, 200, 170, 0.3);
  border: 1px solid rgba(180, 200, 170, 0.5);
  border-radius: 50%;
  color: #333333;
  text-decoration: none;
  transition: all 0.3s ease;
}

.shreyanka-footer .footer-social-link:hover {
  background: rgba(180, 200, 170, 0.5);
  border-color: rgba(180, 200, 170, 0.7);
  color: #333333;
  transform: translateY(-2px);
}

.shreyanka-footer .footer-social-link i {
  font-size: 16px;
}

.shreyanka-footer .footer-social-link .sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.shreyanka-footer .footer-copyright {
  font-size: 14px;
  color: #666666;
}

.shreyanka-footer .footer-copyright p {
  margin: 0;
}

/* Dark theme overrides for footer */
body.dark-theme .shreyanka-footer {
  background: rgba(0, 0, 0, 1);
  border-top-color: rgba(40, 40, 40, 0.5);
}

body.dark-theme .shreyanka-footer .footer-info .footer-name {
  color: #ffffff;
}

body.dark-theme .shreyanka-footer .footer-info .footer-title {
  color: #cccccc;
}

body.dark-theme .shreyanka-footer .footer-social-link {
  background: rgba(40, 40, 40, 0.5);
  border-color: rgba(40, 40, 40, 0.7);
  color: #ffffff;
}

body.dark-theme .shreyanka-footer .footer-social-link:hover {
  background: rgba(40, 40, 40, 0.7);
  border-color: rgba(40, 40, 40, 0.9);
  color: #ffffff;
}

body.dark-theme .shreyanka-footer .footer-copyright {
  color: #cccccc;
}

/* Mobile responsive for header and footer */
@media (max-width: 768px) {
  .shreyanka-header .navbar {
    padding: 0.75rem 0;
  }

  .shreyanka-header .brand-name {
    font-size: 20px;
  }

  .shreyanka-header .navbar-nav {
    margin-top: 1rem;
    text-align: center;
  }

  .shreyanka-header .navbar-nav .nav-link {
    padding: 0.75rem 1rem;
    margin: 0.25rem 0;
  }

  .shreyanka-header .theme-toggle-btn {
    margin: 1rem auto 0;
  }

  .shreyanka-footer .footer-content {
    gap: 1rem;
  }

  .shreyanka-footer .footer-social {
    gap: 0.75rem;
  }
}